#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细的OCR评估报告，包含图片展示
"""

import json
import os
import shutil
from pathlib import Path
import base64

def copy_images_to_report_folder():
    """复制图片到报告文件夹"""
    report_dir = Path("report")
    images_dir = report_dir / "images"
    
    # 创建报告目录
    report_dir.mkdir(exist_ok=True)
    images_dir.mkdir(exist_ok=True)
    
    # 复制图片文件
    source_images_dir = Path("test_dataset/images")
    copied_files = {}
    
    for category_dir in source_images_dir.iterdir():
        if category_dir.is_dir():
            category = category_dir.name
            target_category_dir = images_dir / category
            target_category_dir.mkdir(exist_ok=True)
            
            for image_file in category_dir.iterdir():
                if image_file.suffix.lower() in ['.png', '.jpg', '.jpeg']:
                    target_file = target_category_dir / image_file.name
                    shutil.copy2(image_file, target_file)
                    copied_files[image_file.name] = f"images/{category}/{image_file.name}"
    
    return copied_files

def generate_detailed_html_report():
    """生成详细的HTML报告"""
    
    # 读取评估结果
    with open('evaluation_results.json', 'r', encoding='utf-8') as f:
        evaluation_data = json.load(f)
    
    # 复制图片文件
    print("复制图片文件...")
    image_paths = copy_images_to_report_folder()
    
    # 生成HTML内容
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR评估详细报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #444;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
            padding-left: 15px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }}
        .summary-card .value {{
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }}
        .category-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .category-card {{
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }}
        .category-card h3 {{
            margin-top: 0;
            color: #333;
            text-transform: capitalize;
        }}
        .stat-row {{
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }}
        .stat-row:last-child {{
            border-bottom: none;
        }}
        .file-item {{
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .file-header {{
            background: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ddd;
        }}
        .file-header:hover {{
            background: #e9ecef;
        }}
        .file-info {{
            display: flex;
            gap: 20px;
            align-items: center;
        }}
        .file-name {{
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }}
        .file-category {{
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }}
        .file-category.mobile {{ background: #28a745; }}
        .file-category.scanned {{ background: #ffc107; color: #333; }}
        .file-category.question {{ background: #dc3545; }}
        .file-stats {{
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #666;
        }}
        .accuracy {{
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 4px;
        }}
        .accuracy.good {{ background: #d4edda; color: #155724; }}
        .accuracy.medium {{ background: #fff3cd; color: #856404; }}
        .accuracy.poor {{ background: #f8d7da; color: #721c24; }}
        .file-content {{
            display: none;
            padding: 25px;
            background: #f8f9fa;
        }}
        .file-content.show {{
            display: block;
        }}
        .content-layout {{
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 25px;
            margin-top: 15px;
        }}
        .image-section {{
            text-align: center;
        }}
        .image-preview {{
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }}
        .text-comparison {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }}
        .text-box {{
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
            max-height: 400px;
            overflow-y: auto;
        }}
        .text-box h4 {{
            margin-top: 0;
            color: #333;
            font-size: 16px;
        }}
        .pred-text {{ border-left: 4px solid #007bff; }}
        .gt-text {{ border-left: 4px solid #28a745; }}
        .text-box pre {{
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }}
        .toggle-btn {{
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
            transition: transform 0.3s;
        }}
        .toggle-btn.rotated {{
            transform: rotate(180deg);
        }}
        .metrics {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
        }}
        .metric {{
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }}
        .metric-label {{
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }}
        .metric-value {{
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }}
        @media (max-width: 1024px) {{
            .content-layout {{
                grid-template-columns: 1fr;
            }}
            .text-comparison {{
                grid-template-columns: 1fr;
            }}
        }}
        @media (max-width: 768px) {{
            .summary-grid {{
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }}
            .file-stats {{
                flex-direction: column;
                gap: 5px;
            }}
        }}
        
        /* 模态框样式 */
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }}
        .modal-content {{
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1200px;
            max-height: 90%;
            object-fit: contain;
        }}
        .close {{
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }}
        .close:hover {{
            color: #bbb;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>OCR文本识别详细评估报告</h1>
        
        <h2>总体统计</h2>
        <div class="summary-grid">
"""
    
    # 添加总体统计
    summary = evaluation_data['summary']
    summary_cards = [
        ('总文件数', summary['total_files']),
        ('成功评估', summary['success_files']),
        ('成功率', f"{summary['success_rate']*100:.1f}%"),
        ('平均准确率', f"{summary['avg_accuracy']*100:.1f}%"),
        ('平均编辑距离', f"{summary['avg_edit_distance']:.1f}"),
        ('预测字符数', f"{summary['total_pred_chars']:,}"),
        ('真实字符数', f"{summary['total_gt_chars']:,}")
    ]
    
    for title, value in summary_cards:
        html_content += f"""
            <div class="summary-card">
                <h3>{title}</h3>
                <p class="value">{value}</p>
            </div>"""
    
    html_content += """
        </div>
        
        <h2>分类别统计</h2>
        <div class="category-stats">
"""
    
    # 添加分类统计
    for category, stats in summary['category_stats'].items():
        html_content += f"""
            <div class="category-card">
                <h3>{category}</h3>
                <div class="stat-row">
                    <span>文件数量:</span>
                    <span>{stats['count']}</span>
                </div>
                <div class="stat-row">
                    <span>平均准确率:</span>
                    <span>{stats['avg_accuracy']*100:.1f}%</span>
                </div>
                <div class="stat-row">
                    <span>平均编辑距离:</span>
                    <span>{stats['avg_edit_distance']:.1f}</span>
                </div>
                <div class="stat-row">
                    <span>预测字符数:</span>
                    <span>{stats['total_pred_chars']:,}</span>
                </div>
                <div class="stat-row">
                    <span>真实字符数:</span>
                    <span>{stats['total_gt_chars']:,}</span>
                </div>
            </div>"""
    
    html_content += """
        </div>
        
        <h2>详细结果</h2>
        <div class="file-list">
"""
    
    # 添加详细文件结果
    for i, evaluation in enumerate(evaluation_data['evaluations']):
        accuracy_class = 'good' if evaluation['accuracy'] > 0.8 else 'medium' if evaluation['accuracy'] > 0.6 else 'poor'
        image_path = image_paths.get(evaluation['filename'], '')
        
        html_content += f"""
            <div class="file-item">
                <div class="file-header" onclick="toggleFileContent({i})">
                    <div class="file-info">
                        <span class="file-name">{evaluation['filename']}</span>
                        <span class="file-category {evaluation['category']}">{evaluation['category']}</span>
                    </div>
                    <div class="file-stats">
                        <span class="accuracy {accuracy_class}">准确率: {evaluation['accuracy']*100:.1f}%</span>
                        <span>编辑距离: {evaluation['edit_distance']}</span>
                        <span>预测: {evaluation['pred_char_count']}字符</span>
                        <span>真实: {evaluation['gt_char_count']}字符</span>
                    </div>
                    <button class="toggle-btn" id="btn-{i}">▼</button>
                </div>
                <div class="file-content" id="content-{i}">
                    <div class="metrics">
                        <div class="metric">
                            <div class="metric-label">准确率</div>
                            <div class="metric-value">{evaluation['accuracy']*100:.1f}%</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">编辑距离</div>
                            <div class="metric-value">{evaluation['edit_distance']}</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">预测字符</div>
                            <div class="metric-value">{evaluation['pred_char_count']}</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">真实字符</div>
                            <div class="metric-value">{evaluation['gt_char_count']}</div>
                        </div>
                    </div>
                    <div class="content-layout">
                        <div class="image-section">
                            <h4>原始图片</h4>"""
        
        if image_path:
            html_content += f"""
                            <img src="{image_path}" alt="{evaluation['filename']}" class="image-preview" onclick="openModal(this.src)">"""
        else:
            html_content += f"""
                            <p>图片未找到: {evaluation['filename']}</p>"""
        
        html_content += f"""
                        </div>
                        <div class="text-comparison">
                            <div class="text-box pred-text">
                                <h4>模型识别结果</h4>
                                <pre>{evaluation.get('pred_text', '无识别结果')}</pre>
                            </div>
                            <div class="text-box gt-text">
                                <h4>标准答案</h4>
                                <pre>{evaluation.get('gt_text', '无标准答案')}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>"""
    
    html_content += """
        </div>
    </div>
    
    <!-- 图片模态框 -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        function toggleFileContent(index) {
            const content = document.getElementById(`content-${index}`);
            const btn = document.getElementById(`btn-${index}`);
            
            if (content.classList.contains('show')) {
                content.classList.remove('show');
                btn.textContent = '▼';
                btn.classList.remove('rotated');
            } else {
                content.classList.add('show');
                btn.textContent = '▲';
                btn.classList.add('rotated');
            }
        }
        
        function openModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = src;
        }
        
        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }
        
        // 点击模态框背景关闭
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>"""
    
    # 保存HTML文件
    report_path = Path("report/detailed_evaluation_report.html")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"详细报告已生成: {report_path}")
    return report_path

if __name__ == '__main__':
    report_path = generate_detailed_html_report()
    print(f"报告生成完成！请打开 {report_path} 查看详细结果。")
