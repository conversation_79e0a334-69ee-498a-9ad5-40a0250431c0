#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三模型OCR评估对比系统
支持原始模型、微调模型和第三个模型的对比评估
"""

import json
import os
from pathlib import Path
from text_count import TextCounter
from text_eval import TextEvaluator

class ThreeModelEvaluator:
    def __init__(self):
        self.text_counter = TextCounter()
        # 启用忽略空格和回车的选项，专注于文本内容评估
        self.text_evaluator = TextEvaluator(ignore_blank=True, ignore_case=False)
        self.results = {
            'summary': {},
            'evaluations': []
        }
    
    def load_data_files(self):
        """加载三个数据文件"""
        try:
            # 加载原始+微调模型结果
            with open('ocr_results_20250801_003409.json', 'r', encoding='utf-8') as f:
                self.base_lora_data = json.load(f)
            print(f"加载base+lora数据: {len(self.base_lora_data)} 个文件")
            
            # 加载第三个模型结果
            with open('test_run_results.json', 'r', encoding='utf-8') as f:
                self.third_model_data = json.load(f)
            print(f"加载第三个模型数据: {len(self.third_model_data['results'])} 个文件")
            
            return True
        except Exception as e:
            print(f"加载数据文件失败: {e}")
            return False
    
    def get_answer_path(self, filename, category):
        """获取答案文件路径"""
        base_name = filename.rsplit('.', 1)[0]
        answer_filename = f"{base_name}-answer.txt"
        answer_path = Path(f"test_dataset/answers/{category}/{answer_filename}")
        return answer_path if answer_path.exists() else None
    
    def load_answer_text(self, answer_path):
        """加载答案文本"""
        try:
            with open(answer_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except:
            return None
    
    def extract_filename_from_path(self, image_path):
        """从路径中提取文件名"""
        return Path(image_path).name
    
    def get_category_from_path(self, image_path):
        """从路径中提取类别"""
        path_parts = Path(image_path).parts
        for part in path_parts:
            if part in ['mobile', 'scanned', 'question']:
                return part
        return 'unknown'
    
    def merge_lines_to_text(self, lines):
        """将lines列表合并为文本"""
        if isinstance(lines, list):
            return '\n'.join(lines)
        return str(lines) if lines else ""
    
    def evaluate_single_file(self, base_lora_item, third_model_item, gt_text):
        """评估单个文件的三个模型结果"""
        filename = self.extract_filename_from_path(base_lora_item['image'])
        category = self.get_category_from_path(base_lora_item['image'])
        
        # 获取三个模型的文本
        base_text = base_lora_item.get('base_text', '')
        lora_text = base_lora_item.get('lora_text', '')
        third_text = self.merge_lines_to_text(third_model_item.get('lines', []))
        
        # 计算字符数
        base_char_count = self.text_counter.count_characters(base_text)
        lora_char_count = self.text_counter.count_characters(lora_text)
        third_char_count = self.text_counter.count_characters(third_text)
        gt_char_count = self.text_counter.count_characters(gt_text)
        
        # 计算编辑距离和准确率
        base_edit_distance = self.text_evaluator.calculate_edit_distance(base_text, gt_text)
        lora_edit_distance = self.text_evaluator.calculate_edit_distance(lora_text, gt_text)
        third_edit_distance = self.text_evaluator.calculate_edit_distance(third_text, gt_text)
        
        max_len = max(len(base_text), len(lora_text), len(third_text), len(gt_text))
        base_accuracy = max(0, 1 - base_edit_distance / max_len) if max_len > 0 else 0
        lora_accuracy = max(0, 1 - lora_edit_distance / max_len) if max_len > 0 else 0
        third_accuracy = max(0, 1 - third_edit_distance / max_len) if max_len > 0 else 0
        
        return {
            'filename': filename,
            'category': category,
            'gt_text': gt_text,
            'gt_char_count': gt_char_count,
            'base_model': {
                'text': base_text,
                'char_count': base_char_count,
                'edit_distance': base_edit_distance,
                'accuracy': base_accuracy
            },
            'lora_model': {
                'text': lora_text,
                'char_count': lora_char_count,
                'edit_distance': lora_edit_distance,
                'accuracy': lora_accuracy
            },
            'third_model': {
                'text': third_text,
                'char_count': third_char_count,
                'edit_distance': third_edit_distance,
                'accuracy': third_accuracy
            }
        }
    
    def run_evaluation(self):
        """运行三模型评估"""
        print("开始三模型对比评估...")
        
        if not self.load_data_files():
            return False
        
        # 创建文件名到数据的映射
        base_lora_map = {}
        for item in self.base_lora_data:
            filename = self.extract_filename_from_path(item['image'])
            base_lora_map[filename] = item
        
        third_model_map = {}
        for item in self.third_model_data['results']:
            filename = item['filename']
            third_model_map[filename] = item
        
        # 找到共同的文件
        common_files = set(base_lora_map.keys()) & set(third_model_map.keys())
        print(f"找到共同文件: {len(common_files)} 个")
        
        evaluations = []
        success_count = 0
        
        for i, filename in enumerate(sorted(common_files), 1):
            print(f"评估进度: {i}/{len(common_files)} - {filename}")
            
            base_lora_item = base_lora_map[filename]
            third_model_item = third_model_map[filename]
            category = self.get_category_from_path(base_lora_item['image'])
            
            # 获取答案文件
            answer_path = self.get_answer_path(filename, category)
            if not answer_path:
                print(f"  警告: 找不到答案文件 {filename}")
                continue
            
            gt_text = self.load_answer_text(answer_path)
            if not gt_text:
                print(f"  警告: 无法读取答案文件 {filename}")
                continue
            
            # 评估单个文件
            evaluation = self.evaluate_single_file(base_lora_item, third_model_item, gt_text)
            evaluations.append(evaluation)
            success_count += 1
        
        # 计算总体统计
        self.calculate_summary_stats(evaluations, len(common_files), success_count)
        self.results['evaluations'] = evaluations
        
        # 保存结果
        output_file = 'three_model_evaluation_results.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n评估完成！结果已保存到: {output_file}")
        return True
    
    def calculate_summary_stats(self, evaluations, total_files, success_count):
        """计算总体统计"""
        if not evaluations:
            return
        
        # 初始化统计数据
        stats = {
            'total_files': total_files,
            'success_files': success_count,
            'success_rate': success_count / total_files if total_files > 0 else 0,
            'base_model': {'total_accuracy': 0, 'total_edit_distance': 0, 'total_chars': 0},
            'lora_model': {'total_accuracy': 0, 'total_edit_distance': 0, 'total_chars': 0},
            'third_model': {'total_accuracy': 0, 'total_edit_distance': 0, 'total_chars': 0},
            'gt_total_chars': 0,
            'category_stats': {}
        }
        
        # 按类别统计
        category_data = {}
        
        for eval_item in evaluations:
            category = eval_item['category']
            if category not in category_data:
                category_data[category] = {
                    'count': 0,
                    'base_model': {'accuracy': 0, 'edit_distance': 0, 'chars': 0},
                    'lora_model': {'accuracy': 0, 'edit_distance': 0, 'chars': 0},
                    'third_model': {'accuracy': 0, 'edit_distance': 0, 'chars': 0},
                    'gt_chars': 0
                }
            
            cat_data = category_data[category]
            cat_data['count'] += 1
            
            # 累加各模型数据
            for model_name in ['base_model', 'lora_model', 'third_model']:
                model_data = eval_item[model_name]
                stats[model_name]['total_accuracy'] += model_data['accuracy']
                stats[model_name]['total_edit_distance'] += model_data['edit_distance']
                stats[model_name]['total_chars'] += model_data['char_count']
                
                cat_data[model_name]['accuracy'] += model_data['accuracy']
                cat_data[model_name]['edit_distance'] += model_data['edit_distance']
                cat_data[model_name]['chars'] += model_data['char_count']
            
            # GT数据
            stats['gt_total_chars'] += eval_item['gt_char_count']
            cat_data['gt_chars'] += eval_item['gt_char_count']
        
        # 计算平均值
        for model_name in ['base_model', 'lora_model', 'third_model']:
            stats[model_name]['avg_accuracy'] = stats[model_name]['total_accuracy'] / success_count
            stats[model_name]['avg_edit_distance'] = stats[model_name]['total_edit_distance'] / success_count
        
        # 计算分类平均值
        for category, cat_data in category_data.items():
            count = cat_data['count']
            stats['category_stats'][category] = {
                'count': count,
                'base_model': {
                    'avg_accuracy': cat_data['base_model']['accuracy'] / count,
                    'avg_edit_distance': cat_data['base_model']['edit_distance'] / count,
                    'total_chars': cat_data['base_model']['chars']
                },
                'lora_model': {
                    'avg_accuracy': cat_data['lora_model']['accuracy'] / count,
                    'avg_edit_distance': cat_data['lora_model']['edit_distance'] / count,
                    'total_chars': cat_data['lora_model']['chars']
                },
                'third_model': {
                    'avg_accuracy': cat_data['third_model']['accuracy'] / count,
                    'avg_edit_distance': cat_data['third_model']['edit_distance'] / count,
                    'total_chars': cat_data['third_model']['chars']
                },
                'gt_total_chars': cat_data['gt_chars']
            }
        
        self.results['summary'] = stats
        
        # 打印结果
        self.print_summary_stats()
    
    def print_summary_stats(self):
        """打印统计结果"""
        stats = self.results['summary']
        
        print(f"\n=== 三模型对比评估结果 ===")
        print(f"总文件数: {stats['total_files']}")
        print(f"成功评估: {stats['success_files']}")
        print(f"成功率: {stats['success_rate']*100:.2f}%")
        
        print(f"\n=== 模型性能对比 ===")
        models = [
            ('原始模型', 'base_model'),
            ('微调模型', 'lora_model'), 
            ('第三模型', 'third_model')
        ]
        
        for model_name, model_key in models:
            model_stats = stats[model_key]
            print(f"{model_name}:")
            print(f"  平均准确率: {model_stats['avg_accuracy']*100:.2f}%")
            print(f"  平均编辑距离: {model_stats['avg_edit_distance']:.2f}")
            print(f"  总字符数: {model_stats['total_chars']:,}")
        
        print(f"\n真实文本总字符数: {stats['gt_total_chars']:,}")
        
        print(f"\n=== 分类别对比 ===")
        for category, cat_stats in stats['category_stats'].items():
            print(f"{category} ({cat_stats['count']}个文件):")
            for model_name, model_key in models:
                model_data = cat_stats[model_key]
                print(f"  {model_name}: {model_data['avg_accuracy']*100:.2f}% 准确率, "
                      f"{model_data['avg_edit_distance']:.2f} 编辑距离")

def main():
    """主函数"""
    evaluator = ThreeModelEvaluator()
    
    if evaluator.run_evaluation():
        print("\n✅ 三模型评估完成！")
        print("📄 结果文件: three_model_evaluation_results.json")
        return 0
    else:
        print("❌ 评估失败")
        return 1

if __name__ == '__main__':
    exit(main())
