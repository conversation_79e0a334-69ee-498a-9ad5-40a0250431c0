# OCR评估报告自动生成工具

## 📋 项目概述

这是一个自动生成OCR文本识别评估报告的工具，能够展示原图、标准答案和模型识别结果的对比分析。

## 🚀 功能特点

- **文本清理**: 使用 `text_cleaner.py` 清理OCR识别结果中的无关内容
- **评测指标计算**: 使用 `text_count.py` 和 `text_eval.py` 计算字符数量和编辑距离
- **HTML报告生成**: 自动生成美观的HTML评估报告，包含：
  - 总体统计数据
  - 分类别详细统计
  - 每个文件的详细对比（原图、模型结果、标准答案）
  - 响应式设计，支持移动端查看
  - 图片放大查看功能

## 📁 文件结构

```
├── text_cleaner.py                    # 文本清理工具
├── text_count.py                      # 字符数量计算
├── text_eval.py                       # 编辑距离计算
├── evaluation_script.py               # 评估脚本
├── auto_generate_html_report.py       # HTML报告生成器
├── test_dataset/                      # 测试数据集
│   ├── images/                        # 原始图片
│   │   ├── mobile/                    # 手机拍摄图片
│   │   ├── scanned/                   # 扫描图片
│   │   └── question/                  # 题目图片
│   └── answers/                       # 标准答案
│       ├── mobile/
│       ├── scanned/
│       └── question/
├── test_dataset_full_results.json     # 原始OCR结果
├── test_dataset_full_results_cleaned.json  # 清理后的OCR结果
├── evaluation_results.json            # 评估结果
└── html_report/                       # 生成的HTML报告
    ├── ocr_evaluation_report.html     # 主报告文件
    └── images/                        # 复制的图片文件
```

## 🔧 使用方法

### 1. 环境准备

```bash
pip install editdistance
```

### 2. 完整流程

#### 步骤1: 文本清理
```bash
python text_cleaner.py test_dataset_full_results.json
```

#### 步骤2: 评估计算
```bash
python evaluation_script.py
```

#### 步骤3: 生成HTML报告
```bash
python auto_generate_html_report.py
```

### 3. 自定义参数

```bash
# 指定自定义路径
python auto_generate_html_report.py \
    --evaluation-file evaluation_results.json \
    --images-dir test_dataset/images \
    --output-dir my_report
```

## 📊 评估指标

- **准确率**: 基于编辑距离计算的文本相似度
- **编辑距离**: 两个文本之间的最小编辑操作数
- **字符数量**: 预测文本和真实文本的字符统计
- **分类统计**: 按图片类型（mobile/scanned/question）分组统计

## 🎨 报告特性

### 总体统计
- 文件总数和成功率
- 平均准确率和编辑距离
- 字符数量统计

### 分类别统计
- 按图片类型分组的详细统计
- 每个类别的性能对比

### 详细结果
- 每个文件的详细信息
- 原图展示（支持点击放大）
- 模型识别结果与标准答案对比
- 可折叠的详细视图

### 交互功能
- 响应式设计，适配各种屏幕尺寸
- 图片模态框放大查看
- 平滑的展开/折叠动画
- 键盘快捷键支持（ESC关闭图片）

## 📈 评估结果示例

当前数据集评估结果：
- **总文件数**: 57个
- **成功率**: 100%
- **平均准确率**: 76.5%
- **分类表现**:
  - Question类别: 82.7% (最佳)
  - Scanned类别: 73.6%
  - Mobile类别: 73.2%

## 🛠️ 技术栈

- **Python**: 核心处理逻辑
- **HTML/CSS/JavaScript**: 前端报告展示
- **编辑距离算法**: 文本相似度计算
- **响应式设计**: 支持多设备查看

## 📝 注意事项

1. 确保图片文件路径正确
2. 答案文件命名格式: `{图片名}-answer.txt`
3. 支持的图片格式: PNG, JPG, JPEG
4. 生成的HTML报告需要在Web服务器环境下查看图片

## 🔄 更新日志

- **v1.0**: 基础功能实现
- **v1.1**: 添加图片展示功能
- **v1.2**: 优化UI设计和交互体验
- **v1.3**: 添加响应式设计和移动端支持

## 📞 支持

如有问题或建议，请联系开发团队。
