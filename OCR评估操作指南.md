# OCR评估系统操作指南

## 📋 概述

本指南将详细说明如何按正确顺序执行OCR文本识别评估，从数据预处理到最终报告生成的完整流程。

## 🎯 评估目标

- 对OCR识别结果进行文本清理
- 计算识别准确率和编辑距离等评测指标
- 生成包含原图、标准答案和识别结果的可视化报告

## 📁 文件结构要求

在开始之前，请确保你的项目目录结构如下：

```
项目根目录/
├── text_cleaner.py                    # 文本清理工具
├── text_count.py                      # 字符数量计算工具
├── text_eval.py                       # 编辑距离计算工具
├── evaluation_script.py               # 主评估脚本
├── auto_generate_html_report.py       # HTML报告生成器
├── test_dataset_full_results.json     # 原始OCR识别结果（必需）
├── test_dataset/                      # 测试数据集目录
│   ├── images/                        # 原始图片目录
│   │   ├── mobile/                    # 手机拍摄图片
│   │   ├── scanned/                   # 扫描图片
│   │   └── question/                  # 题目图片
│   └── answers/                       # 标准答案目录
│       ├── mobile/                    # 手机图片对应答案
│       ├── scanned/                   # 扫描图片对应答案
│       └── question/                  # 题目图片对应答案
```

## 🔧 环境准备

### 1. 安装依赖

```bash
pip install editdistance
```

### 2. 验证文件完整性

确保以下文件存在：
- ✅ `test_dataset_full_results.json` - OCR识别结果
- ✅ `test_dataset/images/` - 包含所有原始图片
- ✅ `test_dataset/answers/` - 包含对应的标准答案文件

## 🚀 执行步骤

### 步骤1: 文本清理 🧹

**目的**: 清理OCR识别结果中的无关内容（如页码、标题等）

```bash
python text_cleaner.py test_dataset_full_results.json
```

**输出文件**: `test_dataset_full_results_cleaned.json`

**预期结果**:
```
文本清理完成！
输入文件: test_dataset_full_results.json
输出文件: test_dataset_full_results_cleaned.json
处理文件数量: 57
```

**注意事项**:
- 如果出现 `SyntaxWarning: invalid escape sequence '\s'` 警告，可以忽略，不影响功能
- 确保生成了 `test_dataset_full_results_cleaned.json` 文件

### 步骤2: 评估计算 📊

**目的**: 计算准确率、编辑距离等评测指标

```bash
python evaluation_script.py
```

**输出文件**: `evaluation_results.json`

**预期结果**:
```
开始评估 57 个文件...
评估进度: 1/57 - 2025050011-s-1.png
...
评估进度: 57/57 - 2025050124-s-1.png

=== 评估结果汇总 ===
总文件数: 57
成功评估文件数: 57
成功率: 100.00%
平均准确率: 0.6521
平均编辑距离: 183.58
预测总字符数: 22174
真实总字符数: 21301

=== 按类别统计 ===
mobile:
  文件数: 19
  平均准确率: 0.5796
  平均编辑距离: 323.84
scanned:
  文件数: 19
  平均准确率: 0.7566
  平均编辑距离: 99.68
question:
  文件数: 19
  平均准确率: 0.6203
  平均编辑距离: 127.21

评估结果已保存到: evaluation_results.json
```

**关键指标说明**:
- **准确率**: 1 - (编辑距离/最大文本长度)，越高越好
- **编辑距离**: 两个文本间的最小编辑操作数，越低越好
- **成功率**: 找到对应答案文件的比例

### 步骤3: 生成HTML报告 🌐

**目的**: 生成包含原图、答案和识别结果的可视化报告

```bash
python auto_generate_html_report.py
```

**输出目录**: `html_report/`

**预期结果**:
```
开始生成OCR评估HTML报告...
成功加载评估数据，共57个文件
复制了 57 个图片文件到输出目录
生成HTML内容...
✅ HTML报告生成成功: html_report\ocr_evaluation_report.html
📁 报告目录: C:\Users\<USER>\html_report

🎉 报告生成完成！
📄 HTML文件: html_report\ocr_evaluation_report.html
🌐 请在浏览器中打开查看详细结果
🚀 已自动在浏览器中打开报告
```

**生成的文件**:
- `html_report/ocr_evaluation_report.html` - 主报告文件
- `html_report/images/` - 所有图片文件的副本

## 📈 当前评估结果

基于最新数据的评估结果：

### 总体表现
- **总文件数**: 57个
- **成功率**: 100%
- **平均准确率**: 65.21%
- **平均编辑距离**: 183.58

### 分类表现排名
1. **Scanned类别**: 75.66% 准确率 ⭐ (最佳)
2. **Question类别**: 62.03% 准确率
3. **Mobile类别**: 57.96% 准确率 (需要改进)

### 性能分析
- **扫描图片** 表现最好，可能因为图像质量较高
- **手机拍摄** 表现相对较差，可能受光照、角度等因素影响
- **题目图片** 表现中等，可能包含复杂的排版格式

## 🎨 HTML报告功能

生成的HTML报告包含以下功能：

### 📊 总体统计
- 文件数量和成功率
- 平均准确率和编辑距离
- 字符数量统计

### 📈 分类统计
- 按图片类型的详细统计
- 性能对比分析

### 📋 详细结果
- 每个文件的完整信息
- **原图展示** - 点击可放大查看
- **模型识别结果** - OCR输出的文本
- **标准答案** - 人工标注的正确文本
- **性能指标** - 准确率、编辑距离等

### 🎯 交互功能
- 响应式设计，支持手机/平板/电脑
- 可折叠的详细视图
- 图片模态框放大
- 平滑动画效果

## 🔍 故障排除

### 常见问题

1. **找不到答案文件**
   - 检查 `test_dataset/answers/` 目录结构
   - 确保答案文件命名格式: `{图片名}-answer.txt`

2. **图片加载失败**
   - 检查 `test_dataset/images/` 目录结构
   - 确保图片格式为 PNG、JPG 或 JPEG

3. **评估结果为0**
   - 检查OCR结果文件格式是否正确
   - 确保答案文件内容不为空

### 重新执行

如果需要重新执行整个流程：

```bash
# 清理之前的结果
rm -f test_dataset_full_results_cleaned.json
rm -f evaluation_results.json
rm -rf html_report/

# 重新执行完整流程
python text_cleaner.py test_dataset_full_results.json
python evaluation_script.py
python auto_generate_html_report.py
```

## 📞 技术支持

如遇到问题，请检查：
1. 文件路径是否正确
2. 依赖包是否已安装
3. 数据文件格式是否符合要求

---

**🎉 完成后，打开 `html_report/ocr_evaluation_report.html` 即可查看完整的评估报告！**
