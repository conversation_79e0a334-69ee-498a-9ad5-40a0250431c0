import editdistance

class TextEvaluator:
    def __init__(self, ignore_blank=False, ignore_case=False):
        self.ignore_blank = ignore_blank
        self.ignore_case = ignore_case

    def preprocess(self, text):
        """文本预处理"""
        if self.ignore_blank:
            text = text.replace(" ", "")
        if self.ignore_case:
            text = text.lower()
        return text

    def calculate_edit_distance(self, gt_text, pred_text):
        """计算编辑距离"""
        gt = self.preprocess(gt_text)
        pred = self.preprocess(pred_text)
        return editdistance.eval(gt, pred)

def read_text_file(path):
    """读取文件内容（整个文件作为单个字符串）"""
    with open(path, "r", encoding="utf-8") as f:
        return f.read().strip()

if __name__ == "__main__":
    # 读取文件
    gt = read_text_file("../gt.txt")
    pred = read_text_file("../pred.txt")

    # 计算编辑距离
    evaluator = TextEvaluator(ignore_blank=False, ignore_case=False)
    #evaluator = TextEvaluator(ignore_blank=True, ignore_case=False)
    ed = evaluator.calculate_edit_distance(gt, pred)

    # 输出结果
    print(f"编辑距离: {ed}")