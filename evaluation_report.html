<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR评估报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
        }
        h2 {
            color: #444;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
            padding-left: 15px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .summary-card .value {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        .category-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .category-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .category-card h3 {
            margin-top: 0;
            color: #333;
            text-transform: capitalize;
        }
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .stat-row:last-child {
            border-bottom: none;
        }
        .file-list {
            margin-top: 20px;
        }
        .file-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
            overflow: hidden;
            background: white;
        }
        .file-header {
            background: #f8f9fa;
            padding: 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ddd;
        }
        .file-header:hover {
            background: #e9ecef;
        }
        .file-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .file-name {
            font-weight: bold;
            color: #333;
        }
        .file-category {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .file-stats {
            display: flex;
            gap: 15px;
            font-size: 14px;
            color: #666;
        }
        .accuracy {
            font-weight: bold;
        }
        .accuracy.good { color: #28a745; }
        .accuracy.medium { color: #ffc107; }
        .accuracy.poor { color: #dc3545; }
        .file-content {
            display: none;
            padding: 20px;
            background: #f8f9fa;
        }
        .file-content.show {
            display: block;
        }
        .text-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        .text-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: white;
            max-height: 300px;
            overflow-y: auto;
        }
        .text-box h4 {
            margin-top: 0;
            color: #333;
        }
        .pred-text { border-left: 4px solid #007bff; }
        .gt-text { border-left: 4px solid #28a745; }
        .toggle-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 5px;
            margin: 10px 0;
        }
        @media (max-width: 768px) {
            .text-comparison {
                grid-template-columns: 1fr;
            }
            .summary-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OCR文本识别评估报告</h1>
        
        <h2>总体统计</h2>
        <div class="summary-grid" id="summaryGrid">
            <!-- 动态生成统计卡片 -->
        </div>
        
        <h2>分类别统计</h2>
        <div class="category-stats" id="categoryStats">
            <!-- 动态生成分类统计 -->
        </div>
        
        <h2>详细结果</h2>
        <div class="file-list" id="fileList">
            <!-- 动态生成文件列表 -->
        </div>
    </div>

    <script>
        // 评估数据将在这里加载
        let evaluationData = null;
        
        // 加载评估数据
        async function loadEvaluationData() {
            try {
                const response = await fetch('evaluation_results.json');
                evaluationData = await response.json();
                renderReport();
            } catch (error) {
                console.error('加载评估数据失败:', error);
                document.getElementById('summaryGrid').innerHTML = '<p>加载数据失败，请确保evaluation_results.json文件存在</p>';
            }
        }
        
        // 渲染报告
        function renderReport() {
            if (!evaluationData) return;
            
            renderSummary();
            renderCategoryStats();
            renderFileList();
        }
        
        // 渲染总体统计
        function renderSummary() {
            const summary = evaluationData.summary;
            const summaryGrid = document.getElementById('summaryGrid');
            
            const cards = [
                { title: '总文件数', value: summary.total_files },
                { title: '成功评估', value: summary.success_files },
                { title: '成功率', value: (summary.success_rate * 100).toFixed(1) + '%' },
                { title: '平均准确率', value: (summary.avg_accuracy * 100).toFixed(1) + '%' },
                { title: '平均编辑距离', value: summary.avg_edit_distance.toFixed(1) },
                { title: '预测字符数', value: summary.total_pred_chars.toLocaleString() },
                { title: '真实字符数', value: summary.total_gt_chars.toLocaleString() }
            ];
            
            summaryGrid.innerHTML = cards.map(card => `
                <div class="summary-card">
                    <h3>${card.title}</h3>
                    <p class="value">${card.value}</p>
                </div>
            `).join('');
        }
        
        // 渲染分类统计
        function renderCategoryStats() {
            const categoryStats = evaluationData.summary.category_stats;
            const categoryStatsDiv = document.getElementById('categoryStats');
            
            categoryStatsDiv.innerHTML = Object.entries(categoryStats).map(([category, stats]) => `
                <div class="category-card">
                    <h3>${category}</h3>
                    <div class="stat-row">
                        <span>文件数量:</span>
                        <span>${stats.count}</span>
                    </div>
                    <div class="stat-row">
                        <span>平均准确率:</span>
                        <span>${(stats.avg_accuracy * 100).toFixed(1)}%</span>
                    </div>
                    <div class="stat-row">
                        <span>平均编辑距离:</span>
                        <span>${stats.avg_edit_distance.toFixed(1)}</span>
                    </div>
                    <div class="stat-row">
                        <span>预测字符数:</span>
                        <span>${stats.total_pred_chars.toLocaleString()}</span>
                    </div>
                    <div class="stat-row">
                        <span>真实字符数:</span>
                        <span>${stats.total_gt_chars.toLocaleString()}</span>
                    </div>
                </div>
            `).join('');
        }
        
        // 渲染文件列表
        function renderFileList() {
            const evaluations = evaluationData.evaluations;
            const fileList = document.getElementById('fileList');
            
            fileList.innerHTML = evaluations.map((evaluation, index) => {
                const accuracyClass = evaluation.accuracy > 0.8 ? 'good' : 
                                    evaluation.accuracy > 0.6 ? 'medium' : 'poor';
                
                return `
                    <div class="file-item">
                        <div class="file-header" onclick="toggleFileContent(${index})">
                            <div class="file-info">
                                <span class="file-name">${evaluation.filename}</span>
                                <span class="file-category">${evaluation.category}</span>
                            </div>
                            <div class="file-stats">
                                <span class="accuracy ${accuracyClass}">准确率: ${(evaluation.accuracy * 100).toFixed(1)}%</span>
                                <span>编辑距离: ${evaluation.edit_distance}</span>
                                <span>预测: ${evaluation.pred_char_count}字符</span>
                                <span>真实: ${evaluation.gt_char_count}字符</span>
                            </div>
                            <button class="toggle-btn">▼</button>
                        </div>
                        <div class="file-content" id="content-${index}">
                            <div class="text-comparison">
                                <div class="text-box pred-text">
                                    <h4>模型识别结果</h4>
                                    <pre>${evaluation.pred_text || '无识别结果'}</pre>
                                </div>
                                <div class="text-box gt-text">
                                    <h4>标准答案</h4>
                                    <pre>${evaluation.gt_text || '无标准答案'}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 切换文件内容显示
        function toggleFileContent(index) {
            const content = document.getElementById(`content-${index}`);
            const btn = content.previousElementSibling.querySelector('.toggle-btn');
            
            if (content.classList.contains('show')) {
                content.classList.remove('show');
                btn.textContent = '▼';
            } else {
                content.classList.add('show');
                btn.textContent = '▲';
            }
        }
        
        // 页面加载完成后加载数据
        document.addEventListener('DOMContentLoaded', loadEvaluationData);
    </script>
</body>
</html>
