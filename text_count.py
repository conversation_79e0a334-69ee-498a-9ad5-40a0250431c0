class TextCounter:
    def __init__(self, ignore_blank=True):
        self.ignore_blank = ignore_blank

    def preprocess(self, text):
        """文本预处理（去除空格）"""
        if self.ignore_blank:
            text = text.replace(" ", "")
        return text

    def count_characters(self, text):
        """计算字符数量（不考虑空格）"""
        processed_text = self.preprocess(text)
        return len(processed_text)

def read_text_file(path):
    """读取文件内容（整个文件作为单个字符串）"""
    with open(path, "r", encoding="utf-8") as f:
        return f.read().strip()

if __name__ == "__main__":
    # 读取文件
    pred = read_text_file("../pred.txt")

    # 计算字符数量（默认忽略空格）
    counter = TextCounter(ignore_blank=True)
    char_count = counter.count_characters(pred)

    # 输出结果
    print(f"Pred文本字符数量（不考虑空格）: {char_count}")