# 三模型OCR评估对比操作指南

## 📋 项目概述

本指南详细说明如何对三个OCR模型进行对比评估：
- **🔵 原始模型** (base_text)
- **🟢 微调模型** (lora_text) 
- **🟡 第三模型** (test_run_results.json)

## 🎯 评估目标

- 对比三个模型的OCR识别性能
- 计算准确率、编辑距离等评测指标
- 生成包含原图和三个模型结果的可视化对比报告

## 📁 数据文件说明

### 输入文件
1. **`ocr_results_20250801_003409.json`** - 包含原始模型和微调模型的结果
   ```json
   [
     {
       "image": "/path/to/image.png",
       "base_text": "原始模型识别结果",
       "lora_text": "微调模型识别结果"
     }
   ]
   ```

2. **`test_run_results.json`** - 第三个模型的结果
   ```json
   {
     "summary": {...},
     "results": [
       {
         "filename": "image.png",
         "category": "mobile",
         "lines": ["第三模型识别结果"]
       }
     ]
   }
   ```

3. **`test_dataset/`** - 测试数据集
   - `images/` - 原始图片 (注意：已移除 `2025050021-s-3.jpg`)
   - `answers/` - 标准答案文件

## 🔧 环境准备

### 1. 安装依赖
```bash
pip install editdistance
```

### 2. 确认文件结构
```
项目根目录/
├── ocr_results_20250801_003409.json      # 原始+微调模型结果
├── test_run_results.json                 # 第三模型结果
├── text_count.py                         # 字符计数工具
├── text_eval.py                          # 编辑距离计算工具
├── three_model_evaluation.py             # 三模型评估脚本
├── three_model_html_generator.py         # HTML报告生成器
└── test_dataset/                         # 测试数据集
    ├── images/                           # 原始图片 (56个文件)
    │   ├── mobile/                       # 手机拍摄 (19个)
    │   ├── scanned/                      # 扫描图片 (18个，移除了2025050021-s-3.jpg)
    │   └── question/                     # 题目图片 (19个)
    └── answers/                          # 标准答案
        ├── mobile/
        ├── scanned/
        └── question/
```

## 🚀 执行步骤

### 步骤1: 三模型评估计算 📊

**目的**: 对比三个模型的性能指标

```bash
python three_model_evaluation.py
```

**输出文件**: `three_model_evaluation_results.json`

**预期结果**:
```
开始三模型对比评估...
加载base+lora数据: 57 个文件
加载第三个模型数据: 56 个文件
找到共同文件: 56 个
评估进度: 1/56 - 2025050011-s-1.png
...
评估进度: 56/56 - 2025050124-s-1.png

=== 三模型对比评估结果 ===
总文件数: 56
成功评估: 56
成功率: 100.00%

=== 模型性能对比 ===
原始模型:
  平均准确率: 79.22%
  平均编辑距离: 115.95
  总字符数: 20,819
微调模型:
  平均准确率: 88.29%
  平均编辑距离: 69.11
  总字符数: 22,186
第三模型:
  平均准确率: 68.20%
  平均编辑距离: 141.79
  总字符数: 20,466

真实文本总字符数: 21,296
```

### 步骤2: 生成三模型对比HTML报告 🌐

**目的**: 生成包含原图和三个模型结果的可视化对比报告

```bash
python three_model_html_generator.py
```

**输出目录**: `three_model_report/`

**预期结果**:
```
开始生成三模型对比HTML报告...
成功加载评估数据，共56个文件
复制了 56 个图片文件到输出目录
生成HTML内容...
✅ HTML报告生成成功: three_model_report\three_model_comparison_report.html
📁 报告目录: C:\Users\<USER>\three_model_report

🎉 三模型对比报告生成完成！
📄 HTML文件: three_model_report\three_model_comparison_report.html
🌐 请在浏览器中打开查看三模型对比结果
🚀 已自动在浏览器中打开报告
```

## 📊 评估结果分析

### 🏆 模型性能排名

1. **🥇 微调模型**: 88.29% 准确率 (最佳)
2. **🥈 原始模型**: 79.22% 准确率
3. **🥉 第三模型**: 68.20% 准确率

### 📈 分类别表现

#### Scanned类别 (18个文件)
- **微调模型**: 95.46% ⭐ (表现最佳)
- **原始模型**: 81.39%
- **第三模型**: 77.31%

#### Question类别 (19个文件)  
- **微调模型**: 92.51% ⭐
- **原始模型**: 83.83%
- **第三模型**: 62.70%

#### Mobile类别 (19个文件)
- **微调模型**: 77.28% ⭐
- **原始模型**: 72.54%
- **第三模型**: 65.08%

### 🔍 关键发现

1. **微调模型在所有类别都表现最佳**，特别是在scanned和question类别
2. **扫描图片识别效果最好**，所有模型在该类别都有较高准确率
3. **手机拍摄图片最具挑战性**，所有模型在该类别准确率相对较低
4. **第三模型在所有类别都表现相对较差**，可能需要进一步优化

## 🎨 HTML报告功能

生成的三模型对比报告包含：

### 📊 总体对比
- 三个模型的整体性能统计
- 准确率、编辑距离、字符数对比
- 彩色卡片展示，便于快速对比

### 📈 分类别对比
- 按图片类型的详细统计
- 每个类别中三个模型的性能对比
- 清晰的数据表格展示

### 📋 详细文件对比
- 每个文件的完整信息展示
- **原图展示** - 点击可放大查看
- **标准答案** - 人工标注的正确文本
- **三个模型结果** - 并排对比显示
- **性能指标** - 每个模型的准确率和编辑距离

### 🎯 交互功能
- 响应式设计，支持各种设备
- 可折叠的详细视图
- 图片模态框放大
- 颜色编码区分不同模型

## 🔄 快速执行

### 完整流程 (两步走)
```bash
# 步骤1: 三模型评估计算
python three_model_evaluation.py

# 步骤2: 生成HTML对比报告  
python three_model_html_generator.py
```

### 自定义参数
```bash
# 指定自定义路径
python three_model_html_generator.py \
    --evaluation-file three_model_evaluation_results.json \
    --images-dir test_dataset/images \
    --output-dir my_three_model_report
```

## 📝 重要说明

1. **文件数量变化**: 由于移除了 `2025050021-s-3.jpg`，scanned类别从19个减少到18个文件
2. **数据匹配**: 系统自动匹配三个数据源中的共同文件进行评估
3. **答案文件**: 确保每个图片都有对应的 `{图片名}-answer.txt` 答案文件
4. **图片格式**: 支持 PNG、JPG、JPEG 格式

## 🔍 故障排除

### 常见问题
1. **文件数量不匹配**: 检查三个数据文件中的文件列表是否一致
2. **找不到答案文件**: 确保答案文件命名正确
3. **图片加载失败**: 检查图片文件路径和格式

### 重新执行
```bash
# 清理之前的结果
rm -f three_model_evaluation_results.json
rm -rf three_model_report/

# 重新执行
python three_model_evaluation.py
python three_model_html_generator.py
```

---

**🎉 完成后，打开 `three_model_report/three_model_comparison_report.html` 即可查看完整的三模型对比报告！**

## 📊 当前评估结果总结

- **总文件数**: 56个 (移除了1个scanned图片)
- **成功率**: 100%
- **最佳模型**: 微调模型 (88.29% 平均准确率)
- **最大改进**: 微调模型在scanned类别达到95.46%准确率
- **待优化**: 第三模型在所有类别都有提升空间
