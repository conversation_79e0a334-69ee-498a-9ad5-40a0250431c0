#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作文OCR结果文本清理工具
用于清理OCR识别结果中的无关内容
"""

import re
import json
import argparse
from typing import Dict, List, Any
from pathlib import Path

class TextCleaner:
    """文本清理器类"""
    
    def __init__(self):
        """初始化清理规则"""
        self.patterns = [
            # 作文相关标识
            r'作文内容',
            r'作文题目',
            r'作文标题',
            
            # 语文试卷页码格式：语文第X面（共Y面）
            r'语文第\s*\d+\s*面（共\s*\d+\s*面）',
            
            # 田字格
            r'田字格',
            
            # 答题区域提示（完整版和简化版）
            r'请在各题目的答题区域内作答。超出黑色虚线边框限定区域的答案无效。',
            r'请在各题目的答题区域内作答。',
            
            # 其他常见的无关内容
            r'题目：',  # 单独的"题目："标识
            r'^\s*题目\s*$',  # 单独一行的"题目"
            
            # 页码相关
            r'第\s*\d+\s*页',
            r'共\s*\d+\s*页',
            
            # 考试相关标识
            r'考生须知',
            r'注意事项',
            r'答题卡',
            
            # 单独的标点符号或无意义字符
            r'^[。，、；：！？""''（）【】\s]*$',
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE | re.MULTILINE) for pattern in self.patterns]
    
    def clean_text(self, text: str) -> str:
        """清理单个文本
        
        Args:
            text: 待清理的文本
            
        Returns:
            清理后的文本
        """
        if not text or not isinstance(text, str):
            return text
        
        cleaned_text = text
        
        # 应用所有清理规则
        for pattern in self.compiled_patterns:
            cleaned_text = pattern.sub('', cleaned_text)
        
        # 清理多余的空白字符
        cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text)  # 移除多余的空行
        cleaned_text = re.sub(r'^\s+|\s+$', '', cleaned_text, flags=re.MULTILINE)  # 移除行首行尾空白
        cleaned_text = cleaned_text.strip()  # 移除整体首尾空白
        
        return cleaned_text
    
    def clean_lines(self, lines: List[str]) -> List[str]:
        """清理文本行列表
        
        Args:
            lines: 文本行列表
            
        Returns:
            清理后的文本行列表
        """
        if not lines or not isinstance(lines, list):
            return lines
        
        cleaned_lines = []
        for line in lines:
            if isinstance(line, str):
                cleaned_line = self.clean_text(line)
                if cleaned_line:  # 只保留非空的清理后文本
                    cleaned_lines.append(cleaned_line)
            else:
                cleaned_lines.append(line)
        
        return cleaned_lines
    
    def process_ocr_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个OCR结果
        
        Args:
            result: OCR结果字典
            
        Returns:
            清理后的OCR结果字典
        """
        if not isinstance(result, dict):
            return result
        
        # 创建结果副本
        cleaned_result = result.copy()
        
        # 清理text_content字段
        if 'text_content' in cleaned_result:
            cleaned_result['text_content'] = self.clean_text(cleaned_result['text_content'])
        
        # 清理lines字段
        if 'lines' in cleaned_result:
            cleaned_result['lines'] = self.clean_lines(cleaned_result['lines'])
        
        return cleaned_result
    
    def process_json_file(self, input_file: str, output_file: str = None) -> Dict[str, Any]:
        """处理JSON文件
        
        Args:
            input_file: 输入JSON文件路径
            output_file: 输出JSON文件路径（可选，默认为输入文件名_cleaned.json）
            
        Returns:
            处理后的数据
        """
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 读取JSON文件
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 处理数据
        if isinstance(data, dict):
            # 处理results字段中的每个结果
            if 'results' in data and isinstance(data['results'], list):
                cleaned_results = []
                for result in data['results']:
                    cleaned_result = self.process_ocr_result(result)
                    cleaned_results.append(cleaned_result)
                
                # 更新数据
                cleaned_data = data.copy()
                cleaned_data['results'] = cleaned_results
                
                # 添加清理信息到summary
                if 'summary' in cleaned_data:
                    cleaned_data['summary']['text_cleaned'] = True
                    cleaned_data['summary']['cleaning_timestamp'] = str(Path().resolve())
            else:
                cleaned_data = data
        else:
            cleaned_data = data
        
        # 确定输出文件路径
        if output_file is None:
            output_file = input_path.parent / f"{input_path.stem}_cleaned{input_path.suffix}"
        
        # 保存清理后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
        
        print(f"文本清理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        
        # 统计信息
        if isinstance(cleaned_data, dict) and 'results' in cleaned_data:
            total_files = len(cleaned_data['results'])
            print(f"处理文件数量: {total_files}")
        
        return cleaned_data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OCR结果文本清理工具')
    parser.add_argument('input_file', help='输入JSON文件路径')
    parser.add_argument('-o', '--output', help='输出JSON文件路径（可选）')
    parser.add_argument('--preview', action='store_true', help='预览清理效果（不保存文件）')
    
    args = parser.parse_args()
    
    try:
        cleaner = TextCleaner()
        
        if args.preview:
            # 预览模式：只显示清理效果
            with open(args.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, dict) and 'results' in data:
                print("=== 文本清理预览 ===")
                for i, result in enumerate(data['results'][:3]):  # 只显示前3个结果
                    if 'text_content' in result:
                        original = result['text_content']
                        cleaned = cleaner.clean_text(original)
                        print(f"\n文件 {i+1}: {result.get('filename', 'Unknown')}")
                        print(f"原始文本长度: {len(original)}")
                        print(f"清理后长度: {len(cleaned)}")
                        print(f"原始文本前100字符: {original[:100]}...")
                        print(f"清理后前100字符: {cleaned[:100]}...")
                        print("-" * 50)
        else:
            # 正常处理模式
            result = cleaner.process_json_file(args.input_file, args.output)
            
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())