#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR结果评测脚本
计算字符数量和编辑距离等评测指标
"""

import json
import os
from pathlib import Path
import editdistance
from text_count import TextCounter
from text_eval import TextEvaluator

class OCREvaluator:
    def __init__(self):
        self.text_counter = TextCounter(ignore_blank=True)
        self.text_evaluator = TextEvaluator(ignore_blank=False, ignore_case=False)
        
    def read_answer_file(self, answer_path):
        """读取答案文件"""
        try:
            with open(answer_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except FileNotFoundError:
            return None
            
    def get_answer_path(self, filename, category):
        """根据文件名和类别获取答案文件路径"""
        # 移除文件扩展名（.png, .jpg等），添加-answer.txt
        base_name = filename.rsplit('.', 1)[0]  # 移除最后一个点及其后的扩展名
        answer_filename = f"{base_name}-answer.txt"
        answer_path = Path(f"test_dataset/answers/{category}/{answer_filename}")
        return answer_path if answer_path.exists() else None
        
    def extract_text_from_result(self, result):
        """从OCR结果中提取文本"""
        # 优先使用text_content，如果为空则使用lines
        text_content = result.get('text_content', '')
        if text_content:
            return text_content
        
        # 如果text_content为空，合并lines
        lines = result.get('lines', [])
        if lines:
            return '\n'.join(lines)
        
        return ''
        
    def evaluate_single_file(self, result):
        """评估单个文件"""
        filename = result.get('filename', '')
        category = result.get('category', '')
        
        # 获取预测文本
        pred_text = self.extract_text_from_result(result)
        
        # 获取答案文本
        answer_path = self.get_answer_path(filename, category)
        if not answer_path:
            return {
                'filename': filename,
                'category': category,
                'status': 'no_answer',
                'pred_char_count': self.text_counter.count_characters(pred_text),
                'gt_char_count': 0,
                'edit_distance': -1,
                'accuracy': 0.0
            }
            
        gt_text = self.read_answer_file(answer_path)
        if gt_text is None:
            return {
                'filename': filename,
                'category': category,
                'status': 'answer_read_error',
                'pred_char_count': self.text_counter.count_characters(pred_text),
                'gt_char_count': 0,
                'edit_distance': -1,
                'accuracy': 0.0
            }
        
        # 计算指标
        pred_char_count = self.text_counter.count_characters(pred_text)
        gt_char_count = self.text_counter.count_characters(gt_text)
        edit_distance = self.text_evaluator.calculate_edit_distance(gt_text, pred_text)
        
        # 计算准确率 (1 - 编辑距离/最大长度)
        # 使用标准答案和预测文本的最大长度作为分母
        max_length = max(len(gt_text), len(pred_text))
        accuracy = 1 - (edit_distance / max_length) if max_length > 0 else 1.0
        
        return {
            'filename': filename,
            'category': category,
            'status': 'success',
            'pred_text': pred_text,
            'gt_text': gt_text,
            'pred_char_count': pred_char_count,
            'gt_char_count': gt_char_count,
            'edit_distance': edit_distance,
            'accuracy': accuracy,
            'answer_path': str(answer_path)
        }
        
    def evaluate_all(self, results_file):
        """评估所有文件"""
        # 读取清理后的结果文件
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        results = data.get('results', [])
        evaluations = []
        
        print(f"开始评估 {len(results)} 个文件...")
        
        for i, result in enumerate(results):
            print(f"评估进度: {i+1}/{len(results)} - {result.get('filename', 'Unknown')}")
            evaluation = self.evaluate_single_file(result)
            evaluations.append(evaluation)
            
        return evaluations
        
    def calculate_summary_stats(self, evaluations):
        """计算汇总统计"""
        total_files = len(evaluations)
        success_files = [e for e in evaluations if e['status'] == 'success']
        
        if not success_files:
            return {
                'total_files': total_files,
                'success_files': 0,
                'success_rate': 0.0,
                'avg_accuracy': 0.0,
                'avg_edit_distance': 0.0,
                'total_pred_chars': 0,
                'total_gt_chars': 0
            }
            
        avg_accuracy = sum(e['accuracy'] for e in success_files) / len(success_files)
        avg_edit_distance = sum(e['edit_distance'] for e in success_files) / len(success_files)
        total_pred_chars = sum(e['pred_char_count'] for e in success_files)
        total_gt_chars = sum(e['gt_char_count'] for e in success_files)
        
        # 按类别统计
        category_stats = {}
        for evaluation in success_files:
            category = evaluation['category']
            if category not in category_stats:
                category_stats[category] = {
                    'count': 0,
                    'total_accuracy': 0.0,
                    'total_edit_distance': 0.0,
                    'total_pred_chars': 0,
                    'total_gt_chars': 0
                }
            
            stats = category_stats[category]
            stats['count'] += 1
            stats['total_accuracy'] += evaluation['accuracy']
            stats['total_edit_distance'] += evaluation['edit_distance']
            stats['total_pred_chars'] += evaluation['pred_char_count']
            stats['total_gt_chars'] += evaluation['gt_char_count']
            
        # 计算每个类别的平均值
        for category, stats in category_stats.items():
            count = stats['count']
            stats['avg_accuracy'] = stats['total_accuracy'] / count
            stats['avg_edit_distance'] = stats['total_edit_distance'] / count
            
        return {
            'total_files': total_files,
            'success_files': len(success_files),
            'success_rate': len(success_files) / total_files,
            'avg_accuracy': avg_accuracy,
            'avg_edit_distance': avg_edit_distance,
            'total_pred_chars': total_pred_chars,
            'total_gt_chars': total_gt_chars,
            'category_stats': category_stats
        }

def main():
    evaluator = OCREvaluator()
    
    # 评估所有文件
    evaluations = evaluator.evaluate_all('test_dataset_full_results_cleaned.json')
    
    # 计算汇总统计
    summary_stats = evaluator.calculate_summary_stats(evaluations)
    
    # 保存评估结果
    evaluation_results = {
        'summary': summary_stats,
        'evaluations': evaluations
    }
    
    with open('evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
        
    print("\n=== 评估结果汇总 ===")
    print(f"总文件数: {summary_stats['total_files']}")
    print(f"成功评估文件数: {summary_stats['success_files']}")
    print(f"成功率: {summary_stats['success_rate']:.2%}")
    print(f"平均准确率: {summary_stats['avg_accuracy']:.4f}")
    print(f"平均编辑距离: {summary_stats['avg_edit_distance']:.2f}")
    print(f"预测总字符数: {summary_stats['total_pred_chars']}")
    print(f"真实总字符数: {summary_stats['total_gt_chars']}")
    
    print("\n=== 按类别统计 ===")
    for category, stats in summary_stats['category_stats'].items():
        print(f"{category}:")
        print(f"  文件数: {stats['count']}")
        print(f"  平均准确率: {stats['avg_accuracy']:.4f}")
        print(f"  平均编辑距离: {stats['avg_edit_distance']:.2f}")
        print(f"  预测字符数: {stats['total_pred_chars']}")
        print(f"  真实字符数: {stats['total_gt_chars']}")
    
    print(f"\n评估结果已保存到: evaluation_results.json")

if __name__ == '__main__':
    main()
