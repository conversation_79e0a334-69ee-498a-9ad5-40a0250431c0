#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三模型OCR评估HTML报告生成器
支持原始模型、微调模型和第三个模型的对比展示
"""

import json
import os
import shutil
from pathlib import Path
from datetime import datetime

class ThreeModelHTMLGenerator:
    def __init__(self, evaluation_file='three_model_evaluation_results.json', 
                 images_dir='test_dataset/images', 
                 output_dir='three_model_report'):
        self.evaluation_file = evaluation_file
        self.images_dir = Path(images_dir)
        self.output_dir = Path(output_dir)
        self.evaluation_data = None
        
    def load_evaluation_data(self):
        """加载评估数据"""
        try:
            with open(self.evaluation_file, 'r', encoding='utf-8') as f:
                self.evaluation_data = json.load(f)
            print(f"成功加载评估数据，共{len(self.evaluation_data['evaluations'])}个文件")
            return True
        except FileNotFoundError:
            print(f"错误：找不到评估文件 {self.evaluation_file}")
            return False
        except json.JSONDecodeError:
            print(f"错误：评估文件 {self.evaluation_file} 格式不正确")
            return False
    
    def setup_output_directory(self):
        """设置输出目录"""
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建图片目录
        images_output_dir = self.output_dir / 'images'
        images_output_dir.mkdir(exist_ok=True)
        
        # 复制图片文件
        copied_count = 0
        for category_dir in self.images_dir.iterdir():
            if category_dir.is_dir():
                category = category_dir.name
                target_category_dir = images_output_dir / category
                target_category_dir.mkdir(exist_ok=True)
                
                for image_file in category_dir.iterdir():
                    if image_file.suffix.lower() in ['.png', '.jpg', '.jpeg']:
                        target_file = target_category_dir / image_file.name
                        shutil.copy2(image_file, target_file)
                        copied_count += 1
        
        print(f"复制了 {copied_count} 个图片文件到输出目录")
        return True
    
    def generate_css_styles(self):
        """生成CSS样式"""
        return """
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f7fa;
            }
            
            .container {
                max-width: 1600px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
                padding: 40px 20px;
                border-radius: 15px;
                margin-bottom: 30px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            }
            
            .header h1 {
                font-size: 2.5em;
                margin-bottom: 10px;
                font-weight: 300;
            }
            
            .header .subtitle {
                font-size: 1.1em;
                opacity: 0.9;
            }
            
            .section {
                background: white;
                margin-bottom: 30px;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                overflow: hidden;
            }
            
            .section-header {
                background: #f8f9fa;
                padding: 20px 30px;
                border-bottom: 1px solid #e9ecef;
                border-left: 5px solid #4CAF50;
            }
            
            .section-header h2 {
                color: #2c3e50;
                font-size: 1.5em;
                font-weight: 500;
            }
            
            .section-content {
                padding: 30px;
            }
            
            .model-comparison-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
                margin-bottom: 30px;
            }
            
            .model-card {
                background: white;
                border-radius: 12px;
                padding: 25px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                border-top: 4px solid;
                transition: transform 0.3s ease;
            }
            
            .model-card:hover {
                transform: translateY(-5px);
            }
            
            .model-card.base { border-top-color: #007bff; }
            .model-card.lora { border-top-color: #28a745; }
            .model-card.third { border-top-color: #ffc107; }
            
            .model-card h3 {
                color: #2c3e50;
                margin-bottom: 20px;
                font-size: 1.3em;
                text-align: center;
            }
            
            .model-stat {
                display: flex;
                justify-content: space-between;
                margin: 12px 0;
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
            }
            
            .model-stat:last-child {
                border-bottom: none;
            }
            
            .file-item {
                background: white;
                border-radius: 12px;
                margin-bottom: 25px;
                box-shadow: 0 2px 15px rgba(0,0,0,0.08);
                overflow: hidden;
            }
            
            .file-header {
                background: #f8f9fa;
                padding: 20px 25px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #e9ecef;
            }
            
            .file-info {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            
            .file-name {
                font-weight: 600;
                color: #2c3e50;
                font-size: 1.1em;
            }
            
            .file-category {
                padding: 5px 12px;
                border-radius: 20px;
                font-size: 0.8em;
                font-weight: 600;
                color: white;
            }
            
            .file-category.mobile { background: #28a745; }
            .file-category.scanned { background: #ffc107; color: #333; }
            .file-category.question { background: #dc3545; }
            
            .model-metrics {
                display: flex;
                gap: 15px;
                align-items: center;
            }
            
            .model-metric {
                text-align: center;
                padding: 8px 12px;
                background: white;
                border-radius: 8px;
                border: 2px solid;
                min-width: 80px;
            }
            
            .model-metric.base { border-color: #007bff; }
            .model-metric.lora { border-color: #28a745; }
            .model-metric.third { border-color: #ffc107; }
            
            .metric-label {
                font-size: 0.7em;
                color: #6c757d;
                margin-bottom: 2px;
            }
            
            .metric-value {
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.9em;
            }
            
            .toggle-btn {
                background: none;
                border: none;
                font-size: 1.5em;
                cursor: pointer;
                color: #6c757d;
                transition: transform 0.3s ease;
            }
            
            .toggle-btn.rotated {
                transform: rotate(180deg);
            }
            
            .file-content {
                display: none;
                padding: 30px;
                background: #fafbfc;
            }
            
            .file-content.show {
                display: block;
            }
            
            .content-layout {
                display: grid;
                grid-template-columns: 300px 1fr;
                gap: 30px;
                align-items: start;
            }
            
            .image-section {
                background: white;
                border-radius: 12px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }
            
            .image-section h4 {
                color: #2c3e50;
                margin-bottom: 15px;
                font-size: 1.1em;
            }
            
            .image-preview {
                max-width: 100%;
                max-height: 350px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                cursor: pointer;
                transition: transform 0.3s ease;
            }
            
            .image-preview:hover {
                transform: scale(1.02);
            }
            
            .text-comparison {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto;
                gap: 20px;
            }
            
            .text-box {
                background: white;
                border-radius: 12px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                max-height: 400px;
                overflow-y: auto;
                border-top: 4px solid;
            }
            
            .text-box.gt { border-top-color: #6c757d; grid-column: 1 / -1; }
            .text-box.base { border-top-color: #007bff; }
            .text-box.lora { border-top-color: #28a745; }
            .text-box.third { border-top-color: #ffc107; }
            
            .text-box h4 {
                color: #2c3e50;
                margin-bottom: 15px;
                font-size: 1.1em;
                padding-bottom: 10px;
                border-bottom: 2px solid #e9ecef;
            }
            
            .text-content {
                font-family: 'Microsoft YaHei', monospace;
                line-height: 1.8;
                white-space: pre-wrap;
                word-wrap: break-word;
                color: #2c3e50;
                font-size: 0.9em;
            }
            
            /* 模态框样式 */
            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.9);
            }
            
            .modal-content {
                margin: auto;
                display: block;
                width: 90%;
                max-width: 1200px;
                max-height: 90%;
                object-fit: contain;
                border-radius: 8px;
            }
            
            .close {
                position: absolute;
                top: 20px;
                right: 35px;
                color: #f1f1f1;
                font-size: 40px;
                font-weight: bold;
                cursor: pointer;
            }
            
            .close:hover {
                color: #bbb;
            }
            
            /* 响应式设计 */
            @media (max-width: 1200px) {
                .model-comparison-grid {
                    grid-template-columns: 1fr;
                }
                
                .content-layout {
                    grid-template-columns: 1fr;
                }
                
                .text-comparison {
                    grid-template-columns: 1fr;
                }
                
                .text-box.gt {
                    grid-column: 1;
                }
            }
            
            @media (max-width: 768px) {
                .container {
                    padding: 10px;
                }
                
                .model-metrics {
                    flex-direction: column;
                    gap: 10px;
                }
                
                .model-metric {
                    min-width: auto;
                    width: 100%;
                }
            }
        </style>
        """

    def generate_javascript(self):
        """生成JavaScript代码"""
        return """
        <script>
            function toggleFileContent(index) {
                const content = document.getElementById(`content-${index}`);
                const btn = document.getElementById(`btn-${index}`);

                if (content.classList.contains('show')) {
                    content.classList.remove('show');
                    btn.classList.remove('rotated');
                } else {
                    content.classList.add('show');
                    btn.classList.add('rotated');
                }
            }

            function openModal(src) {
                const modal = document.getElementById('imageModal');
                const modalImg = document.getElementById('modalImage');
                modal.style.display = 'block';
                modalImg.src = src;

                document.addEventListener('keydown', handleKeyPress);
            }

            function closeModal() {
                const modal = document.getElementById('imageModal');
                modal.style.display = 'none';
                document.removeEventListener('keydown', handleKeyPress);
            }

            function handleKeyPress(event) {
                if (event.key === 'Escape') {
                    closeModal();
                }
            }

            window.onclick = function(event) {
                const modal = document.getElementById('imageModal');
                if (event.target === modal) {
                    closeModal();
                }
            }
        </script>
        """

    def generate_summary_section(self):
        """生成总体统计部分"""
        summary = self.evaluation_data['summary']

        html = f"""
        <div class="section">
            <div class="section-header">
                <h2>📊 三模型对比总览</h2>
            </div>
            <div class="section-content">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h3>总体信息</h3>
                    <p>总文件数: <strong>{summary['total_files']}</strong> |
                       成功评估: <strong>{summary['success_files']}</strong> |
                       成功率: <strong>{summary['success_rate']*100:.1f}%</strong></p>
                </div>

                <div class="model-comparison-grid">
                    <div class="model-card base">
                        <h3>🔵 原始模型</h3>
                        <div class="model-stat">
                            <span>平均准确率:</span>
                            <span><strong>{summary['base_model']['avg_accuracy']*100:.2f}%</strong></span>
                        </div>
                        <div class="model-stat">
                            <span>平均编辑距离:</span>
                            <span><strong>{summary['base_model']['avg_edit_distance']:.2f}</strong></span>
                        </div>
                        <div class="model-stat">
                            <span>总字符数:</span>
                            <span><strong>{summary['base_model']['total_chars']:,}</strong></span>
                        </div>
                    </div>

                    <div class="model-card lora">
                        <h3>🟢 微调模型</h3>
                        <div class="model-stat">
                            <span>平均准确率:</span>
                            <span><strong>{summary['lora_model']['avg_accuracy']*100:.2f}%</strong></span>
                        </div>
                        <div class="model-stat">
                            <span>平均编辑距离:</span>
                            <span><strong>{summary['lora_model']['avg_edit_distance']:.2f}</strong></span>
                        </div>
                        <div class="model-stat">
                            <span>总字符数:</span>
                            <span><strong>{summary['lora_model']['total_chars']:,}</strong></span>
                        </div>
                    </div>

                    <div class="model-card third">
                        <h3>🟡 第三模型</h3>
                        <div class="model-stat">
                            <span>平均准确率:</span>
                            <span><strong>{summary['third_model']['avg_accuracy']*100:.2f}%</strong></span>
                        </div>
                        <div class="model-stat">
                            <span>平均编辑距离:</span>
                            <span><strong>{summary['third_model']['avg_edit_distance']:.2f}</strong></span>
                        </div>
                        <div class="model-stat">
                            <span>总字符数:</span>
                            <span><strong>{summary['third_model']['total_chars']:,}</strong></span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                    <h4>📝 标准答案统计</h4>
                    <p>真实文本总字符数: <strong>{summary['gt_total_chars']:,}</strong></p>
                </div>
            </div>
        </div>
        """
        return html

    def generate_category_section(self):
        """生成分类统计部分"""
        category_stats = self.evaluation_data['summary']['category_stats']

        html = """
        <div class="section">
            <div class="section-header">
                <h2>📈 分类别三模型对比</h2>
            </div>
            <div class="section-content">
        """

        for category, stats in category_stats.items():
            html += f"""
                <div style="margin-bottom: 30px;">
                    <h3 style="text-align: center; margin-bottom: 20px; text-transform: capitalize;">
                        {category} 类别 ({stats['count']}个文件)
                    </h3>
                    <div class="model-comparison-grid">
                        <div class="model-card base">
                            <h4>🔵 原始模型</h4>
                            <div class="model-stat">
                                <span>准确率:</span>
                                <span><strong>{stats['base_model']['avg_accuracy']*100:.2f}%</strong></span>
                            </div>
                            <div class="model-stat">
                                <span>编辑距离:</span>
                                <span><strong>{stats['base_model']['avg_edit_distance']:.2f}</strong></span>
                            </div>
                        </div>

                        <div class="model-card lora">
                            <h4>🟢 微调模型</h4>
                            <div class="model-stat">
                                <span>准确率:</span>
                                <span><strong>{stats['lora_model']['avg_accuracy']*100:.2f}%</strong></span>
                            </div>
                            <div class="model-stat">
                                <span>编辑距离:</span>
                                <span><strong>{stats['lora_model']['avg_edit_distance']:.2f}</strong></span>
                            </div>
                        </div>

                        <div class="model-card third">
                            <h4>🟡 第三模型</h4>
                            <div class="model-stat">
                                <span>准确率:</span>
                                <span><strong>{stats['third_model']['avg_accuracy']*100:.2f}%</strong></span>
                            </div>
                            <div class="model-stat">
                                <span>编辑距离:</span>
                                <span><strong>{stats['third_model']['avg_edit_distance']:.2f}</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
            """

        html += """
            </div>
        </div>
        """
        return html

    def generate_file_item(self, evaluation, index):
        """生成单个文件的HTML"""
        filename = evaluation['filename']
        category = evaluation['category']
        gt_text = evaluation['gt_text']

        base_model = evaluation['base_model']
        lora_model = evaluation['lora_model']
        third_model = evaluation['third_model']

        image_path = f"images/{category}/{filename}"

        html = f"""
        <div class="file-item">
            <div class="file-header" onclick="toggleFileContent({index})">
                <div class="file-info">
                    <span class="file-name">{filename}</span>
                    <span class="file-category {category}">{category}</span>
                </div>
                <div class="model-metrics">
                    <div class="model-metric base">
                        <div class="metric-label">原始模型</div>
                        <div class="metric-value">{base_model['accuracy']*100:.1f}%</div>
                    </div>
                    <div class="model-metric lora">
                        <div class="metric-label">微调模型</div>
                        <div class="metric-value">{lora_model['accuracy']*100:.1f}%</div>
                    </div>
                    <div class="model-metric third">
                        <div class="metric-label">第三模型</div>
                        <div class="metric-value">{third_model['accuracy']*100:.1f}%</div>
                    </div>
                </div>
                <button class="toggle-btn" id="btn-{index}">▼</button>
            </div>
            <div class="file-content" id="content-{index}">
                <div class="content-layout">
                    <div class="image-section">
                        <h4>📷 原始图片</h4>
                        <img src="{image_path}" alt="{filename}" class="image-preview"
                             onclick="openModal(this.src)"
                             onerror="this.parentElement.innerHTML='<p>图片加载失败: {filename}</p>'">
                    </div>
                    <div class="text-comparison">
                        <div class="text-box gt">
                            <h4>✅ 标准答案 ({evaluation['gt_char_count']} 字符)</h4>
                            <div class="text-content">{gt_text}</div>
                        </div>
                        <div class="text-box base">
                            <h4>🔵 原始模型 ({base_model['char_count']} 字符, 编辑距离: {base_model['edit_distance']})</h4>
                            <div class="text-content">{base_model['text']}</div>
                        </div>
                        <div class="text-box lora">
                            <h4>🟢 微调模型 ({lora_model['char_count']} 字符, 编辑距离: {lora_model['edit_distance']})</h4>
                            <div class="text-content">{lora_model['text']}</div>
                        </div>
                        <div class="text-box third">
                            <h4>🟡 第三模型 ({third_model['char_count']} 字符, 编辑距离: {third_model['edit_distance']})</h4>
                            <div class="text-content">{third_model['text']}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """
        return html

    def generate_details_section(self):
        """生成详细结果部分"""
        evaluations = self.evaluation_data['evaluations']

        html = """
        <div class="section">
            <div class="section-header">
                <h2>📋 详细三模型对比结果</h2>
            </div>
            <div class="section-content">
        """

        for i, evaluation in enumerate(evaluations):
            html += self.generate_file_item(evaluation, i)

        html += """
            </div>
        </div>
        """
        return html

    def generate_complete_html(self):
        """生成完整的HTML报告"""
        current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")

        html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三模型OCR对比评估报告</title>
    {self.generate_css_styles()}
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>三模型OCR对比评估报告</h1>
            <div class="subtitle">原始模型 vs 微调模型 vs 第三模型 | 生成时间: {current_time}</div>
        </div>

        {self.generate_summary_section()}
        {self.generate_category_section()}
        {self.generate_details_section()}
    </div>

    <!-- 图片模态框 -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    {self.generate_javascript()}
</body>
</html>"""
        return html

    def generate_report(self):
        """生成完整的HTML报告"""
        print("开始生成三模型对比HTML报告...")

        # 1. 加载评估数据
        if not self.load_evaluation_data():
            return False

        # 2. 设置输出目录
        if not self.setup_output_directory():
            return False

        # 3. 生成HTML内容
        print("生成HTML内容...")
        html_content = self.generate_complete_html()

        # 4. 保存HTML文件
        output_file = self.output_dir / 'three_model_comparison_report.html'
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"✅ HTML报告生成成功: {output_file}")
            print(f"📁 报告目录: {self.output_dir.absolute()}")
            return output_file
        except Exception as e:
            print(f"❌ 保存HTML文件失败: {e}")
            return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='生成三模型OCR对比评估HTML报告')
    parser.add_argument('--evaluation-file', '-e',
                       default='three_model_evaluation_results.json',
                       help='评估结果JSON文件路径')
    parser.add_argument('--images-dir', '-i',
                       default='test_dataset/images',
                       help='图片目录路径')
    parser.add_argument('--output-dir', '-o',
                       default='three_model_report',
                       help='输出目录路径')

    args = parser.parse_args()

    # 创建报告生成器
    generator = ThreeModelHTMLGenerator(
        evaluation_file=args.evaluation_file,
        images_dir=args.images_dir,
        output_dir=args.output_dir
    )

    # 生成报告
    result = generator.generate_report()

    if result:
        print(f"\n🎉 三模型对比报告生成完成！")
        print(f"📄 HTML文件: {result}")
        print(f"🌐 请在浏览器中打开查看三模型对比结果")

        # 尝试自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(f'file://{result.absolute()}')
            print(f"🚀 已自动在浏览器中打开报告")
        except:
            print(f"💡 请手动在浏览器中打开: {result.absolute()}")
    else:
        print("❌ 报告生成失败")
        return 1

    return 0

if __name__ == '__main__':
    exit(main())
